package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.DDABillTO
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/dda")
@Secured(SecurityRule.IS_AUTHENTICATED)
class DDAController(
    private val messagePublisher: MessagePublisher,
    private val sqsConfiguration: SQSMessageHandlerConfiguration,
) {
    private val logger = LoggerFactory.getLogger(DDAController::class.java)

    @Post
    fun receiveDDABill(@Body ddaBillTO: DDABillTO): HttpResponse<*> {
        val markers = Markers.append("barcode", ddaBillTO.numcodbarras)

        return try {
            messagePublisher.sendMessage(
                queueName = sqsConfiguration.ddaBills,
                body = ddaBillTO,
            )

            logger.info(markers, "DDAController#receiveDDABill")

            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "DDAController#receiveDDABill", e)
            HttpResponse.serverError(mapOf("status" to "error", "message" to "Failed to process DDA bill"))
        }
    }
}